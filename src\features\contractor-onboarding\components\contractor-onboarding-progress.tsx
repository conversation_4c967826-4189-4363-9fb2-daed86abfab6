import React from 'react';
import { type ContractorStep3FormValues } from '../schemas/contractor-onboarding-schemas';

interface ContractorOnboardingProgressProps {
  currentStep: number;
  step3Data: ContractorStep3FormValues | null;
}

export const ContractorOnboardingProgress =
  React.memo<ContractorOnboardingProgressProps>(
    ({ currentStep, step3Data }) => {
      const showStep4 =
        step3Data?.companyRegistrationType === 'create' || currentStep === 4;

      return (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {/* Step 1: Personal Information */}
            <div className="flex items-center space-x-4">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= 1
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                1
              </div>
              <span
                className={`text-sm font-medium ${
                  currentStep >= 1 ? 'text-foreground' : 'text-muted-foreground'
                }`}
              >
                Personal Information
              </span>
            </div>

            <div className="flex-1 mx-4">
              <div className="h-1 bg-muted rounded-full">
                <div
                  className={`h-1 bg-primary rounded-full transition-all duration-300 ${
                    currentStep >= 2 ? 'w-full' : 'w-0'
                  }`}
                />
              </div>
            </div>

            {/* Step 2: Role-specific Information */}
            <div className="flex items-center space-x-4">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= 2
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                2
              </div>
              <span
                className={`text-sm font-medium ${
                  currentStep >= 2 ? 'text-foreground' : 'text-muted-foreground'
                }`}
              >
                Role Information
              </span>
            </div>

            <div className="flex-1 mx-4">
              <div className="h-1 bg-muted rounded-full">
                <div
                  className={`h-1 bg-primary rounded-full transition-all duration-300 ${
                    currentStep >= 3 ? 'w-full' : 'w-0'
                  }`}
                />
              </div>
            </div>

            {/* Step 3: Company Setup */}
            <div className="flex items-center space-x-4">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= 3
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                3
              </div>
              <span
                className={`text-sm font-medium ${
                  currentStep >= 3 ? 'text-foreground' : 'text-muted-foreground'
                }`}
              >
                Company Setup
              </span>
            </div>

            {/* Only show step 4 if creating a company */}
            {showStep4 && (
              <>
                <div className="flex-1 mx-4">
                  <div className="h-1 bg-muted rounded-full">
                    <div
                      className={`h-1 bg-primary rounded-full transition-all duration-300 ${
                        currentStep >= 4 ? 'w-full' : 'w-0'
                      }`}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div
                    className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                      currentStep >= 4
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-muted-foreground'
                    }`}
                  >
                    4
                  </div>
                  <span
                    className={`text-sm font-medium ${
                      currentStep >= 4
                        ? 'text-foreground'
                        : 'text-muted-foreground'
                    }`}
                  >
                    Company Creation
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
      );
    },
  );

ContractorOnboardingProgress.displayName = 'ContractorOnboardingProgress';
