name: PR Validation

on:
  pull_request:
    branches: [develop, main]

jobs:
  validate:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run TypeScript type checking
        run: pnpm type-check

      - name: Run ESLint
        run: pnpm lint

      - name: Run build
        run: pnpm build

      - name: Check for build artifacts
        run: |
          if [ ! -d "build" ]; then
            echo "❌ Build directory not found!"
            exit 1
          fi
          echo "✅ Build completed successfully!"
