import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ChevronLeft, ChevronRight, X, FileText } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SectionHeader } from '@/components/ui/section-header';
import { FileUploadDropzone } from '@/components/ui/file-upload-dropzone';
import {
  useContractorTranslations,
  useCommonTranslations,
} from '@/hooks/use-translations';
import {
  type ContractorStep1FormValues,
  type ContractorStep2FormValues,
} from '../schemas/contractor-onboarding-schemas';

interface ContractorOnboardingStep2Props {
  form: UseFormReturn<ContractorStep2FormValues>;
  onSubmit: (values: ContractorStep2FormValues) => void;
  onPrevious: () => void;
  selectedRole: ContractorStep1FormValues['role'];
}

export const ContractorOnboardingStep2 =
  React.memo<ContractorOnboardingStep2Props>(
    ({ form, onSubmit, onPrevious, selectedRole }) => {
      const t = useContractorTranslations();
      const tCommon = useCommonTranslations();

      // Watch lift list files for dynamic management
      const liftListFiles = form.watch('liftListFiles') || [];
      const registrationCertFile = form.watch('registrationCertFile');

      // Format file size helper
      const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      };

      // Handle lift list files change
      const handleLiftListFilesChange = (files: File[]) => {
        form.setValue('liftListFiles', files);
      };

      // Handle registration certificate file change
      const handleRegistrationCertFileChange = (files: File[]) => {
        if (files.length > 0) {
          form.setValue('registrationCertFile', files[0]);
        } else {
          form.setValue('registrationCertFile', undefined);
        }
      };

      // Remove file from lift list
      const removeLiftListFile = (index: number) => {
        const currentFiles = form.getValues('liftListFiles') || [];
        const newFiles = currentFiles.filter((_, i) => i !== index);
        form.setValue('liftListFiles', newFiles);
      };

      // Render role-specific content
      const renderRoleSpecificFields = () => {
        switch (selectedRole) {
          case 'cp':
            return (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-primary">
                      {t('onboarding.step2.cp.title')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Name */}
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step2.cp.name')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step2.cp.namePlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Category */}
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step2.cp.category')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={t(
                                    'onboarding.step2.cp.categoryPlaceholder',
                                  )}
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="category-a">
                                {t('onboarding.step2.cp.categoryA')}
                              </SelectItem>
                              <SelectItem value="category-b">
                                {t('onboarding.step2.cp.categoryB')}
                              </SelectItem>
                              <SelectItem value="category-c">
                                {t('onboarding.step2.cp.categoryC')}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* IC Number */}
                      <FormField
                        control={form.control}
                        name="icNo"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('onboarding.step2.cp.icNo')}{' '}
                              <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t(
                                  'onboarding.step2.cp.icNoPlaceholder',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* CP Number */}
                      <FormField
                        control={form.control}
                        name="cpNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('onboarding.step2.cp.cpNumber')}{' '}
                              <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t(
                                  'onboarding.step2.cp.cpNumberPlaceholder',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Telephone */}
                      <FormField
                        control={form.control}
                        name="tel"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('onboarding.step2.cp.tel')}{' '}
                              <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="tel"
                                placeholder={t(
                                  'onboarding.step2.cp.telPlaceholder',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Email */}
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('onboarding.step2.cp.email')}{' '}
                              <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder={t(
                                  'onboarding.step2.cp.emailPlaceholder',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>{' '}
                    {/* List of Lift Files */}
                    <div className="space-y-4">
                      <FormLabel>
                        {t('onboarding.step2.cp.liftListFiles')}{' '}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Upload your lift list files. Minimum file size: 10MB.
                        Maximum 5 files allowed.
                      </p>

                      <FileUploadDropzone
                        onFilesChange={handleLiftListFilesChange}
                        accept=".pdf,.doc,.docx,.xlsx,.xls"
                        maxSize={100 * 1024 * 1024} // 100MB max size
                        maxFiles={5}
                        files={liftListFiles}
                        className="border-dashed border-2 border-gray-300"
                      />

                      {/* Display selected files */}
                      {liftListFiles.length > 0 && (
                        <div className="space-y-3">
                          <p className="text-sm font-medium text-gray-700">
                            Selected Files ({liftListFiles.length}/5):
                          </p>
                          <div className="space-y-2">
                            {liftListFiles.map((file, index) => (
                              <div
                                key={`${file.name}-${index}`}
                                className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 transition-colors"
                              >
                                <FileText className="h-5 w-5 text-blue-500 flex-shrink-0" />
                                <div className="flex-1 min-w-0">
                                  <p
                                    className="text-sm font-medium text-gray-900 truncate"
                                    title={file.name}
                                  >
                                    {file.name}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    {formatFileSize(file.size)}
                                  </p>
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeLiftListFile(index)}
                                  className="h-8 w-8 p-0 hover:bg-destructive/10"
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              </div>
                            ))}
                          </div>

                          {/* Validation message */}
                          {liftListFiles.some(
                            (file) => file.size < 10 * 1024 * 1024,
                          ) && (
                            <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded-md">
                              ⚠️ Some files are smaller than the minimum
                              requirement of 10MB. Please ensure your files
                              contain sufficient lift information.
                            </p>
                          )}
                        </div>
                      )}
                    </div>{' '}
                    {/* Registration Certificate File */}
                    <div className="space-y-4">
                      <FormLabel>
                        {t('onboarding.step2.cp.registrationCertFile')}{' '}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Upload your registration certificate. Minimum file size:
                        10MB.
                      </p>

                      <FileUploadDropzone
                        onFilesChange={handleRegistrationCertFileChange}
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        maxSize={100 * 1024 * 1024} // 100MB max size
                        maxFiles={1}
                        files={
                          registrationCertFile ? [registrationCertFile] : []
                        }
                        className="border-dashed border-2 border-gray-300"
                      />

                      {/* Display selected file */}
                      {registrationCertFile && (
                        <div className="space-y-3">
                          <p className="text-sm font-medium text-gray-700">
                            Selected File:
                          </p>
                          <div className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 transition-colors">
                            <FileText className="h-5 w-5 text-blue-500 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              <p
                                className="text-sm font-medium text-gray-900 truncate"
                                title={registrationCertFile.name}
                              >
                                {registrationCertFile.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {formatFileSize(registrationCertFile.size)}
                              </p>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                form.setValue('registrationCertFile', undefined)
                              }
                              className="h-8 w-8 p-0 hover:bg-destructive/10"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>

                          {/* Validation message */}
                          {registrationCertFile.size < 10 * 1024 * 1024 && (
                            <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded-md">
                              ⚠️ File is smaller than the minimum requirement of
                              10MB. Please ensure your certificate file contains
                              sufficient information.
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            );

          case 'admin':
            return (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-primary">
                      {t('onboarding.step2.admin.title')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step2.admin.name')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step2.admin.namePlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            {t('onboarding.step2.admin.nameHelp')}
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>
            );

          case 'technician':
            return (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-primary">
                      {t('onboarding.step2.technician.title')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step2.technician.name')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step2.technician.namePlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            {t('onboarding.step2.technician.nameHelp')}
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>
            );

          default:
            return null;
        }
      };

      return (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
            <SectionHeader
              number={2}
              title={t('onboarding.step2.title', {
                role: t(`onboarding.step1.${selectedRole}`),
              })}
            />

            {renderRoleSpecificFields()}

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                className="px-6 py-3"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                {tCommon('back')}
              </Button>

              <Button
                type="submit"
                className="px-8 py-3"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting
                  ? tCommon('loading')
                  : t('onboarding.step2.nextButton')}
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </form>
        </Form>
      );
    },
  );

ContractorOnboardingStep2.displayName = 'ContractorOnboardingStep2';
