import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ChevronLeft, ChevronRight, Upload, Plus, X } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SectionHeader } from '@/components/ui/section-header';
import {
  useContractorTranslations,
  useCommonTranslations,
} from '@/hooks/use-translations';
import {
  type ContractorStep1FormValues,
  type ContractorStep2FormValues,
} from '../schemas/contractor-onboarding-schemas';

interface ContractorOnboardingStep2Props {
  form: UseFormReturn<ContractorStep2FormValues>;
  onSubmit: (values: ContractorStep2FormValues) => void;
  onPrevious: () => void;
  selectedRole: ContractorStep1FormValues['role'];
}

export const ContractorOnboardingStep2 =
  React.memo<ContractorOnboardingStep2Props>(
    ({ form, onSubmit, onPrevious, selectedRole }) => {
      const t = useContractorTranslations();
      const tCommon = useCommonTranslations();

      // Watch lift list URLs for dynamic management
      const liftListUrls = form.watch('liftListUrls') || [];

      // Add new lift list URL field
      const addLiftListUrl = () => {
        const currentUrls = form.getValues('liftListUrls') || [];
        form.setValue('liftListUrls', [...currentUrls, '']);
      };

      // Remove lift list URL field
      const removeLiftListUrl = (index: number) => {
        const currentUrls = form.getValues('liftListUrls') || [];
        const newUrls = currentUrls.filter((_, i) => i !== index);
        form.setValue('liftListUrls', newUrls);
      };

      // Update lift list URL
      const updateLiftListUrl = (index: number, value: string) => {
        const currentUrls = form.getValues('liftListUrls') || [];
        const newUrls = [...currentUrls];
        newUrls[index] = value;
        form.setValue('liftListUrls', newUrls);
      };

      // Render role-specific content
      const renderRoleSpecificFields = () => {
        switch (selectedRole) {
          case 'cp':
            return (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-primary">
                      {t('onboarding.step2.cp.title')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Name */}
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step2.cp.name')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step2.cp.namePlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Category */}
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step2.cp.category')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={t(
                                    'onboarding.step2.cp.categoryPlaceholder',
                                  )}
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="category-a">
                                {t('onboarding.step2.cp.categoryA')}
                              </SelectItem>
                              <SelectItem value="category-b">
                                {t('onboarding.step2.cp.categoryB')}
                              </SelectItem>
                              <SelectItem value="category-c">
                                {t('onboarding.step2.cp.categoryC')}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* IC Number */}
                      <FormField
                        control={form.control}
                        name="icNo"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('onboarding.step2.cp.icNo')}{' '}
                              <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t(
                                  'onboarding.step2.cp.icNoPlaceholder',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* CP Number */}
                      <FormField
                        control={form.control}
                        name="cpNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('onboarding.step2.cp.cpNumber')}{' '}
                              <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t(
                                  'onboarding.step2.cp.cpNumberPlaceholder',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Telephone */}
                      <FormField
                        control={form.control}
                        name="tel"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('onboarding.step2.cp.tel')}{' '}
                              <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="tel"
                                placeholder={t(
                                  'onboarding.step2.cp.telPlaceholder',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Email */}
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('onboarding.step2.cp.email')}{' '}
                              <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder={t(
                                  'onboarding.step2.cp.emailPlaceholder',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* List of Lift URLs */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <FormLabel>
                          {t('onboarding.step2.cp.liftListUrls')}{' '}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addLiftListUrl}
                          className="gap-1"
                        >
                          <Plus className="w-3 h-3" />
                          {t('onboarding.step2.cp.addLiftUrl')}
                        </Button>
                      </div>

                      {liftListUrls.map((url, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            placeholder={t(
                              'onboarding.step2.cp.liftUrlPlaceholder',
                            )}
                            value={url}
                            onChange={(e) =>
                              updateLiftListUrl(index, e.target.value)
                            }
                            className="flex-1"
                          />
                          {liftListUrls.length > 1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeLiftListUrl(index)}
                              className="shrink-0"
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      ))}

                      {liftListUrls.length === 0 && (
                        <div className="text-center py-4 border-2 border-dashed border-muted-foreground/20 rounded-lg">
                          <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">
                            {t('onboarding.step2.cp.noLiftUrls')}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Registration Certification URL */}
                    <FormField
                      control={form.control}
                      name="registrationCertUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step2.cp.registrationCertUrl')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step2.cp.registrationCertUrlPlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            {t('onboarding.step2.cp.registrationCertUrlHelp')}
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>
            );

          case 'admin':
            return (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-primary">
                      {t('onboarding.step2.admin.title')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step2.admin.name')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step2.admin.namePlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            {t('onboarding.step2.admin.nameHelp')}
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>
            );

          case 'technician':
            return (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-primary">
                      {t('onboarding.step2.technician.title')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step2.technician.name')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step2.technician.namePlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            {t('onboarding.step2.technician.nameHelp')}
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>
            );

          default:
            return null;
        }
      };

      return (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
            <SectionHeader
              number={2}
              title={t('onboarding.step2.title', {
                role: t(`onboarding.step1.${selectedRole}`),
              })}
            />

            {renderRoleSpecificFields()}

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                className="px-6 py-3"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                {tCommon('back')}
              </Button>

              <Button
                type="submit"
                className="px-8 py-3"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting
                  ? tCommon('loading')
                  : t('onboarding.step2.nextButton')}
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </form>
        </Form>
      );
    },
  );

ContractorOnboardingStep2.displayName = 'ContractorOnboardingStep2';
