import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { SectionHeader } from '@/components/ui/section-header';
import {
  useCommonTranslations,
  useContractorTranslations,
} from '@/hooks/use-translations';
import { ChevronLeft } from 'lucide-react';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { type ContractorStep3FormValues } from '../schemas/contractor-onboarding-schemas';

interface ContractorOnboardingStep3Props {
  form: UseFormReturn<ContractorStep3FormValues>;
  onSubmit: (values: ContractorStep3FormValues) => void;
  onPrevious: () => void;
  companyRegistrationType: 'create' | 'join';
}

export const ContractorOnboardingStep3 =
  React.memo<ContractorOnboardingStep3Props>(
    ({ form, onSubmit, onPrevious, companyRegistrationType }) => {
      const t = useContractorTranslations();
      const tCommon = useCommonTranslations();

      return (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
            <SectionHeader number={3} title={t('onboarding.step3.title')} />

            <div className="grid grid-cols-1 gap-8 lg:gap-12">
              {/* Company Registration Type Selection */}
              <FormField
                control={form.control}
                name="companyRegistrationType"
                render={({ field }) => (
                  <FormItem className="space-y-6">
                    <FormLabel className="text-lg font-semibold">
                      {t('onboarding.step3.registrationType')}
                    </FormLabel>
                    <div className="space-y-4">
                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                          field.value === 'create'
                            ? 'border-primary bg-primary/5'
                            : 'border-border hover:border-border/80'
                        }`}
                        onClick={() => field.onChange('create')}
                      >
                        <div className="flex items-start space-x-3">
                          <input
                            type="radio"
                            value="create"
                            checked={field.value === 'create'}
                            onChange={() => field.onChange('create')}
                            className="mt-1"
                          />
                          <div>
                            <h3 className="font-semibold">
                              {t('onboarding.step3.createCompany')}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {t('onboarding.step3.createCompanyDescription')}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                          field.value === 'join'
                            ? 'border-primary bg-primary/5'
                            : 'border-border hover:border-border/80'
                        }`}
                        onClick={() => field.onChange('join')}
                      >
                        <div className="flex items-start space-x-3">
                          <input
                            type="radio"
                            value="join"
                            checked={field.value === 'join'}
                            onChange={() => field.onChange('join')}
                            className="mt-1"
                          />
                          <div>
                            <h3 className="font-semibold">
                              {t('onboarding.step3.joinCompany')}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {t('onboarding.step3.joinCompanyDescription')}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </FormItem>
                )}
              />

              {/* Special Code Input - Only show if joining a company */}
              {companyRegistrationType === 'join' && (
                <FormField
                  control={form.control}
                  name="specialCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t('onboarding.step3.specialCode')}{' '}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t(
                            'onboarding.step3.specialCodePlaceholder',
                          )}
                          {...field}
                          className="font-mono"
                        />
                      </FormControl>
                      <p className="text-sm text-muted-foreground">
                        {t('onboarding.step3.specialCodeHelp')}
                      </p>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* Navigation */}
            <div className="flex justify-between pt-6 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="w-4 h-4" />
                {tCommon('previous')}
              </Button>

              <Button type="submit">
                {companyRegistrationType === 'create'
                  ? t('onboarding.step3.continueToCreate')
                  : t('onboarding.step3.joinNow')}
              </Button>
            </div>
          </form>
        </Form>
      );
    },
  );

ContractorOnboardingStep3.displayName = 'ContractorOnboardingStep3';
