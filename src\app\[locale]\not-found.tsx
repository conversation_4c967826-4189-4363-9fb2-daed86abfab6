'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { AlertTriangle, Home } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

/**
 * Locale-specific 404 Not Found page for Next.js 14+ App Router
 * Follows SimPLE design and coding conventions with full i18n support
 * This page is used when a 404 occurs within a locale route
 */
export default function LocaleNotFound() {
  const t = useTranslations('notFound');

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-2xl mx-auto">
        <Card className="overflow-hidden border-destructive/20">
          <CardHeader className="text-center bg-gradient-to-r from-destructive/5 to-destructive/10 pb-8">
            <div className="flex justify-center mb-6">
              <div className="relative">
                {/* Large 404 with subtle animation */}
                <div className="text-8xl md:text-9xl font-bold text-destructive/20 select-none animate-pulse">
                  {t('title')}
                </div>
                {/* Warning icon overlay */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <AlertTriangle className="h-16 w-16 md:h-20 md:w-20 text-destructive/60" />
                </div>
              </div>
            </div>
            <CardTitle className="text-2xl md:text-3xl font-bold text-foreground mb-2">
              {t('description')}
            </CardTitle>
            <CardDescription className="text-lg text-muted-foreground max-w-md mx-auto">
              The page you&apos;re looking for doesn&apos;t exist or has been
              moved. Let&apos;s get you back on track.
            </CardDescription>
          </CardHeader>

          <CardContent className="text-center py-8">
            <div className="space-y-4">
              {/* Primary action button */}
              <Button asChild size="lg" className="px-8">
                <Link href="/">
                  <Home className="h-4 w-4 mr-2" />
                  {t('backButton')}
                </Link>
              </Button>

              {/* Secondary actions */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
                <Button asChild variant="outline">
                  <Link href="/dashboard">Dashboard</Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/auth-portal">Sign In</Link>
                </Button>
              </div>
            </div>

            {/* Help text */}
            <div className="mt-8 p-4 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>Need help?</strong> If you believe this is an error,
                please contact our support team.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
