import { useForceProfileRefresh } from '@/hooks/use-force-profile-refresh';
import { uploadToOBS } from '@/lib/obs-upload';
import { supabase } from '@/lib/supabase';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type { ContractorFullFormValues } from '../schemas/contractor-onboarding-schemas';

// Helper function to upload multiple files
async function uploadFiles(files: File[], folder: string): Promise<string[]> {
  if (!files || files.length === 0) return [];

  const uploadPromises = files.map(async (file) => {
    try {
      const url = await uploadToOBS({
        file,
        folder,
        onProgress: (_progress) => {
          // Progress tracking can be added here if needed
        },
      });
      return url;
    } catch (error) {
      console.error(`Failed to upload ${file.name}:`, error);
      throw new Error(
        `Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  });

  return Promise.all(uploadPromises);
}

export function useCompleteContractorOnboarding() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const forceProfileRefresh = useForceProfileRefresh();
  return useMutation({
    mutationKey: ['contractor-onboarding', 'complete'],
    mutationFn: async (values: ContractorFullFormValues) => {
      // Get current user
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      // Upload files first if they exist (for future use when creating competent persons)
      // Note: Currently these files are uploaded but not stored in database
      // They will be used when creating competent person records in the future
      try {
        // Upload registration certificate file if provided
        if (values.registrationCertFile) {
          await uploadFiles(
            [values.registrationCertFile],
            'contractor-certificates',
          );
        }

        // Upload lift list files if provided
        if (values.liftListFiles && values.liftListFiles.length > 0) {
          await uploadFiles(values.liftListFiles, 'contractor-lift-lists');
        }
      } catch (error) {
        throw new Error(
          `File upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
      }

      // Update profile with onboarding data
      const { error: profileError } = await supabase
        .from('users')
        .update({
          name:
            values.fullName ||
            user.user_metadata?.full_name ||
            user.email?.split('@')[0],
          phone_number: values.phoneNumber || user.user_metadata?.phone_number,
          onboarding_completed: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (profileError) {
        throw new Error(`Profile update failed: ${profileError.message}`);
      }

      // Handle company operations
      if (values.companyRegistrationType === 'create') {
        // Convert company name to uppercase for consistency
        const uppercaseCompanyName = values.company_name!.toUpperCase().trim();

        // Create new contractor company with retry logic for code collisions
        let attempts = 0;
        const maxAttempts = 5;
        let companyData = null;
        let companyError = null;

        while (attempts < maxAttempts) {
          const { data, error } = await supabase
            .from('contractors')
            .insert({
              name: uppercaseCompanyName,
              contractor_type: values.company_type!,
              hotline: values.company_hotline || '',
              oem_name: values.oem_name || null,
              code: values.code!,
              created_by: user.id,
              updated_by: user.id,
            })
            .select()
            .single();

          companyData = data;
          companyError = error;

          // If no error, break out of retry loop
          if (!companyError) {
            break;
          }

          // Handle specific constraint violations
          if (companyError.code === '23505') {
            if (companyError.message.includes('name')) {
              throw new Error(
                `Company name "${uppercaseCompanyName}" already exists. Please choose a different name.`,
              );
            } else if (companyError.message.includes('code')) {
              // Generate a new code and retry
              const { generateCompanyCode } = await import('@/lib/utils');
              values.code = generateCompanyCode();
              attempts++;
              continue;
            }
          }

          // For other errors, don't retry
          break;
        }

        if (companyError) {
          if (attempts >= maxAttempts) {
            throw new Error(
              'Unable to generate a unique company code after multiple attempts. Please try again.',
            );
          }
          throw new Error(`Company creation failed: ${companyError.message}`);
        }

        // Check if company was created successfully
        if (!companyData) {
          throw new Error('Company creation failed: No company data returned');
        }

        // Update user with contractor information to associate user with the company
        const { error: contractorError } = await supabase
          .from('users')
          .update({
            contractor_id: companyData.id,
            onboarding_completed: true,
            updated_by: user.id,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id);

        if (contractorError) {
          throw new Error(
            `Failed to associate user with company: ${contractorError.message}`,
          );
        }

        return { type: 'create', company: companyData };
      } else if (values.companyRegistrationType === 'join') {
        // Convert special code to uppercase for consistency (company codes should be uppercase)
        const uppercaseSpecialCode = values.specialCode!.toUpperCase().trim();

        // Find contractor company by code
        const { data: companyData, error: companyLookupError } = await supabase
          .from('contractors')
          .select('*')
          .eq('code', uppercaseSpecialCode)
          .single();

        if (companyLookupError || !companyData) {
          throw new Error(
            `Company not found with code "${uppercaseSpecialCode}". Please check the code and try again.`,
          );
        }

        // Check if user already has a contractor_id (already part of a company)
        const { data: existingUser } = await supabase
          .from('users')
          .select('contractor_id')
          .eq('id', user.id)
          .single();

        if (existingUser?.contractor_id) {
          throw new Error('You are already a member of a company');
        } // Join the company by updating user's contractor_id
        const { error: userUpdateError } = await supabase
          .from('users')
          .update({
            contractor_id: companyData.id,
            onboarding_completed: true,
            updated_by: user.id,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id);

        if (userUpdateError) {
          throw new Error(`Failed to join company: ${userUpdateError.message}`);
        }

        return { type: 'join', company: companyData };
      }

      return { type: 'profile_only' };
    },
    onSuccess: async (result) => {
      try {
        // Clear onboarding cookie to force refresh (only on client)
        if (typeof document !== 'undefined') {
          document.cookie =
            'onboarding_completed=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        }

        // Set a flag to indicate recent completion for fallback refresh (only on client)
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem('onboarding_just_completed', 'true');
        }

        // Show success message first
        if (result?.type === 'create') {
          toast.success(
            'Company created successfully! Welcome to your new workspace.',
          );
        } else if (result?.type === 'join') {
          toast.success('Successfully joined the company!');
        } else {
          toast.success('Profile setup completed!');
        } // Invalidate all relevant queries and wait for completion
        await Promise.all([
          queryClient.invalidateQueries({ queryKey: ['user-with-profile'] }),
          queryClient.invalidateQueries({ queryKey: ['profile'] }),
          queryClient.invalidateQueries({ queryKey: ['contractor-profile'] }),
          queryClient.invalidateQueries({ queryKey: ['contractors'] }),
          queryClient.invalidateQueries({ queryKey: ['permissions'] }),
          queryClient.invalidateQueries({ queryKey: ['user'] }),
        ]);

        // Use dedicated force refresh function for comprehensive cache clearing
        await forceProfileRefresh(); // Wait a bit longer to ensure cache is properly updated
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Navigate to projects after successful onboarding completion
        router.push('/projects');
      } catch (error) {
        console.error('Error during post-completion cleanup:', error);
        // Still navigate to projects even if cache cleanup fails
        router.push('/projects');
      }
    },
    onError: (error: Error) => {
      console.error('Onboarding completion error:', error);
      toast.error(
        error.message || 'Failed to complete onboarding. Please try again.',
      );
    },
  });
}
