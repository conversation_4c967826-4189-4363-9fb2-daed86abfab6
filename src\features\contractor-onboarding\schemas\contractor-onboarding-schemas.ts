import { z } from 'zod';

// Step 1 Schema - Personal Information
export const step1Schema = z.object({
  fullName: z
    .string()
    .min(1, 'Full name is required')
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must not exceed 100 characters'),
  icNumber: z
    .string()
    .min(1, 'IC Number is required')
    .regex(/^\d{6}-\d{2}-\d{4}$/, 'IC Number must be in format 123456-78-9012'),
  phoneNumber: z
    .string()
    .min(1, 'Phone number is required')
    .regex(
      /^(\+?6?01[0-46-9]-*[0-9]{7,8})$/,
      'Please enter a valid Malaysian phone number',
    ),
  role: z.enum(['cp', 'admin', 'technician'], {
    required_error: 'Please select a role',
  }),
});

// Step 2 Schema - Role-specific Information
export const step2Schema = z.object({
  // Common fields
  name: z
    .string()
    .min(1, 'Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must not exceed 100 characters'), // CP-specific fields
  category: z.string().optional(),
  icNo: z.string().optional(),
  cpNumber: z.string().optional(),
  tel: z.string().optional(),
  email: z.string().email('Please enter a valid email').optional(),
  liftListFiles: z
    .array(z.instanceof(File))
    .min(1, 'Please select at least one lift list file')
    .optional(),
  liftListUrls: z.array(z.string().url('Please enter a valid URL')).optional(),
  registrationCertFile: z
    .instanceof(File, {
      message: 'Please select a valid registration certificate file',
    })
    .optional(),
  registrationCertUrl: z.string().url('Please enter a valid URL').optional(),
});

// Step 3 Schema - Company Setup
export const step3Schema = z
  .object({
    companyRegistrationType: z.enum(['create', 'join'], {
      required_error: 'Please select a company registration option',
    }),
    specialCode: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.companyRegistrationType === 'join') {
        return data.specialCode && data.specialCode.trim().length > 0;
      }
      return true;
    },
    {
      message: 'Special code is required when joining a company',
      path: ['specialCode'],
    },
  );

// Step 4 Schema - Company Creation (only when "create" is selected)
export const step4Schema = z
  .object({
    company_name: z
      .string()
      .min(2, {
        message: 'Company name must be at least 2 characters.',
      })
      .max(100, {
        message: 'Company name must not exceed 100 characters.',
      })
      .transform((val) => val.trim().toUpperCase()), // Transform to uppercase
    company_type: z.enum(['COMPETENT_FIRM', 'NON_COMPETENT_FIRM', 'OEM'], {
      required_error: 'Please select a company type.',
    }),
    company_hotline: z
      .string()
      .min(8, {
        message: 'Company hotline must be at least 8 characters.',
      })
      .max(20, {
        message: 'Company hotline must not exceed 20 characters.',
      }),
    oem_name: z.string().optional(),
    code: z.string().regex(/^\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/, {
      message: 'Company code must be in format YYMM-XXXX-XXXX.',
    }),
  })
  .refine(
    (data) => {
      // If company type is OEM, oem_name is required
      if (data.company_type === 'OEM') {
        return data.oem_name && data.oem_name.length >= 2;
      }
      return true;
    },
    {
      message: 'OEM name is required for OEM companies.',
      path: ['oem_name'],
    },
  );

// Company form schema - same as step3 but without the uppercase transform for standalone use
export const companyFormSchema = z
  .object({
    company_name: z
      .string()
      .min(2, {
        message: 'Company name must be at least 2 characters.',
      })
      .max(100, {
        message: 'Company name must not exceed 100 characters.',
      }),
    company_type: z.enum(['COMPETENT_FIRM', 'NON_COMPETENT_FIRM', 'OEM'], {
      required_error: 'Please select a company type.',
    }),
    company_hotline: z
      .string()
      .min(8, {
        message: 'Company hotline must be at least 8 characters.',
      })
      .max(20, {
        message: 'Company hotline must not exceed 20 characters.',
      }),
    oem_name: z.string().optional(),
    code: z.string().regex(/^\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/, {
      message: 'Company code must be in format YYMM-XXXX-XXXX.',
    }),
  })
  .refine(
    (data) => {
      // If company type is OEM, oem_name is required
      if (data.company_type === 'OEM') {
        return data.oem_name && data.oem_name.length >= 2;
      }
      return true;
    },
    {
      message: 'OEM name is required for OEM companies.',
      path: ['oem_name'],
    },
  );

// Type definitions for contractor onboarding
export type ContractorStep1FormValues = z.infer<typeof step1Schema>;
export type ContractorStep2FormValues = z.infer<typeof step2Schema>;
export type ContractorStep3FormValues = z.infer<typeof step3Schema>;
export type ContractorStep4FormValues = z.infer<typeof step4Schema>;
export type ContractorFullFormValues = ContractorStep1FormValues &
  ContractorStep2FormValues &
  ContractorStep3FormValues &
  Partial<ContractorStep4FormValues>;

// Company form types
export type CompanyFormValues = z.infer<typeof companyFormSchema>;

// Legacy type aliases for backward compatibility (to be removed after refactor)
export type Step1FormValues = ContractorStep1FormValues;
export type Step2FormValues = ContractorStep2FormValues;
export type Step3FormValues = ContractorStep3FormValues;
export type Step4FormValues = ContractorStep4FormValues;
export type FullFormValues = ContractorFullFormValues;
