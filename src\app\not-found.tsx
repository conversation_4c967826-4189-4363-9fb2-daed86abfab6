import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { AlertTriangle, Home } from 'lucide-react';
import Link from 'next/link';

/**
 * Custom 404 Not Found page for Next.js 14+ App Router
 * Follows SimPLE design and coding conventions
 *
 * Note: This is a server component that provides fallback text in both languages
 * since the locale context is not available at the root not-found level
 */
export default function NotFound() {
  return (
    <html lang="en">
      <body className="antialiased">
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <div className="w-full max-w-2xl mx-auto">
            <Card className="overflow-hidden border-destructive/20">
              <CardHeader className="text-center bg-gradient-to-r from-destructive/5 to-destructive/10 pb-8">
                <div className="flex justify-center mb-6">
                  <div className="relative">
                    {/* Large 404 with subtle animation */}
                    <div className="text-8xl md:text-9xl font-bold text-destructive/20 select-none animate-pulse">
                      404
                    </div>
                    {/* Warning icon overlay */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <AlertTriangle className="h-16 w-16 md:h-20 md:w-20 text-destructive/60" />
                    </div>
                  </div>
                </div>
                <CardTitle className="text-2xl md:text-3xl font-bold text-foreground mb-2">
                  Page Not Found
                </CardTitle>
                <CardDescription className="text-lg text-muted-foreground max-w-md mx-auto mb-4">
                  The page you&apos;re looking for doesn&apos;t exist or has
                  been moved. Let&apos;s get you back on track.
                </CardDescription>
                {/* Malay translation */}
                <CardDescription className="text-base text-muted-foreground/80 max-w-md mx-auto italic">
                  Halaman tidak dijumpai. Mari kembali ke halaman utama.
                </CardDescription>
              </CardHeader>

              <CardContent className="text-center py-8">
                <div className="space-y-4">
                  {/* Primary action button */}
                  <Button asChild size="lg" className="px-8">
                    <Link href="/">
                      <Home className="h-4 w-4 mr-2" />
                      Back to Home • Kembali ke Utama
                    </Link>
                  </Button>

                  {/* Secondary actions */}
                  <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
                    <Button asChild variant="outline">
                      <Link href="/projects">Projects • Projek</Link>
                    </Button>
                    <Button asChild variant="outline">
                      <Link href="/auth-portal">Sign In • Log Masuk</Link>
                    </Button>
                  </div>
                </div>

                {/* Help text */}
                <div className="mt-8 p-4 bg-muted/50 rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    <strong>Need help?</strong> If you believe this is an error,
                    please contact our support team.
                  </p>
                  <p className="text-xs text-muted-foreground/80 mt-1 italic">
                    Perlukan bantuan? Jika anda percaya ini adalah ralat, sila
                    hubungi pasukan sokongan kami.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </body>
    </html>
  );
}
